# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
yarn-error.log

# testing
coverage

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem
.idea/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# docusaurus
.docusaurus
apps/docs/api-catalog/docs/*

#native
apps/mobile/vendor/bundle/
apps/mobile/ios/main.jsbundle
apps/mobile/ios/Pods/
apps/mobile/ios/assets/__node_modules
apps/mobile/android/app/src/main/assets/index.android.bundle
apps/mobile/android/app/src/main/res/**/__node_modules_*
.metro-health-check*
.gradle

#package distribution
dist