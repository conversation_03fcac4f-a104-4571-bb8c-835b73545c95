import { setupVu } from "./core/setup";
import {
  accessHomePageExec,
  creditCardTransactionExec,
  estatementEnrollment,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageCr2Exec,
  accessHomePageMs,
  createTransferInsideCIB,
  accountTransaction,
  accountTransactionExec,
  createOutSideCibTransfer,
  creditCardTransaction,
  accountTransactionDetails,
  accountTransactionDetailsExec,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userAccountMovement: accountTransaction("load"),
    // userAccountMovementDetails: accountTransactionDetails('load')
  },
};

export {
  accessHomePageExec,
  accessHomePageCr2Exec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accountTransactionDetailsExec,
};
