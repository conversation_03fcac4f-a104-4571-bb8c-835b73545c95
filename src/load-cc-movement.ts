import { setupVu } from "./core/setup";
import {
  accessHomePageExec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollment,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageCr2Exec,
  accessHomePageMs,
  createTransferInsideCIB,
  accountTransaction,
  createOutSideCibTransfer,
  creditCardTransaction,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userCreditCardMovement: creditCardTransaction("load"),
  },
};

export {
  accessHomePageExec,
  accessHomePageCr2Exec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
};
