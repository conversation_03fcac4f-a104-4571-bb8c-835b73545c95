import { getUserDetailsCr2 } from "../actions/users";
import { login, logout, logoutCr2 } from "../actions/login";

import exec from "k6/execution";
import { getProductListCr2 } from "../actions/accounts";
import { loginCr2NoCert } from "../actions/login/loginCr2NoCert";
import { fail, sleep } from "k6";
import { callHomepage } from "../actions/homepage";
import { getTargetVus } from "../core/helpers/getTargetVus";
import {
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { CustomHeaders } from "../core/httpx";

const waitTimeAfterLogin = 40;
const waitTimeAfterVu = 146;

export function accessHomePageCr2Exec() {
  const user = globalThis.users[exec.vu.idInTest - 1];
  const csrf = loginCr2NoCert(user.username, user.password);
  sleep(waitTimeAfterLogin);
  if (csrf) {
    getUserDetailsCr2(csrf);
    getProductListCr2(csrf);
    logoutCr2(csrf);
  } else {
    sleep(waitTimeAfterVu);
    fail("The user was not logged in");
  }
  sleep(waitTimeAfterVu);
}

export function accessHomePageExec() {
  const user: any = getNextUser(exec.vu.idInTest, globalThis.users);
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: users`,
      );
    }
    const deviceId = `${user.username}_deviceId5`
    const token = login(user.username, user.password, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      sleep(waitTimeAfterLogin);
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }

  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} | ${user.password} - ${error?.message} - ${error?.status}`,
      );
    }
    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function accessHomePageCr2(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  return accessHomePage(type, accessHomePageCr2Exec.name);
}

export function accessHomePageMs(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  return accessHomePage(type, accessHomePageExec.name);
}

export function accessHomePage(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
  exec: string,
) {
  const targetVUs = getTargetVus(type, "accessHomePage");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "300s",
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec,
      };
    default:
      throw new Error("Invalid type");
  }
}
