import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function listBeneficiaries(page: number, customHeaders: CustomHeaders) {
  group("List beneficiaries", () => {
    const res = api.get(
      `/v1/beneficiaries?page=${page}`,
      "listBeneficiaries",
      customHeaders,
    );

    check(res, {
      "list beneficiaries success": (r) => r.status === 200,
    });
  });
}
