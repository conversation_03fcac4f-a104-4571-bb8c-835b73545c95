import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function statmentEntrollmentPreCondition(email: string, customHeaders: CustomHeaders) {
  return group("Get Account Statment Pre condition", () => {
    const res = api.post(
      "/v1/accounts/e-statement-subscriptions",
      {
        email,
      },
      "statmentEntrollment",
      customHeaders,
    );

    check(res, {
      "is status 428": (r) => r.status === 428,
    });
    return res.json("signature")?.toString() || "";
  });
}

export function statmentEntrollConfirmation(
  email: string,
  signature: string,
  customHeaders: CustomHeaders,
) {
  group(" Account Statment Confirmation", () => {
    const res = api.post(
      "/v1/accounts/e-statement-subscriptions",
      {
        email,
        signature,
      },
      "statmentEntrollment",
      customHeaders,
    );

    check(res, {
      "is status 200": (r) => r.status === 200,
    });
  });
}
