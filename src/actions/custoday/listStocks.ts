import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";
export function listStocks(customHeader: CustomHeaders) 
{
  
  return group("List Stocks", () => {
    const res = api.get(
      "/v1/accounts/securities-portfolios",
      "listStocks",
      customHeader,
    );
     const Success=
    check(res, {
      "listStocks response is success": (r) => r.status === 200,
    });

    if ( !Success) {
      throw new Error(
        `Use Case Failure - retrieve Custody list failed - ${res.status} - ${res.body} - ${res.request.url}`);
      }
        try {
      return res.json("data.securitiesPortfolios");
    } 
    catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return stock. - ${e.message}`,
      );
      
      }
    });
  }
  
  