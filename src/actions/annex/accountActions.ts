import { group, check } from 'k6';
import { CustomHeaders } from "../../core/httpx";

/**
 * Creates a new deposit
 * @param body The deposit request body
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function createDeposit(body: any, customHeaders: CustomHeaders) {
    return group("Create Deposit", () => {
        const res = globalThis.api.post("/v1/deposits", body, "create deposit", customHeaders);
        check(res, {
            "Create deposit successful": (r) => r.status >= 200 && r.status < 300
        });
        return res;
    });
}

/**
 * Downloads annex document for a specific deposit
 * @param depositId The ID of the deposit
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getDownloadAnnexForDeposit(depositId: string, customHeaders: CustomHeaders) {
    return group("Download Annex for Deposit", () => {
        const res = globalThis.api.get(`/v1/deposits/annex/${depositId}`, "download annex for deposit", customHeaders);
        check(res, {
            "Download annex for deposit successful": (r) => r.status >= 200 && r.status < 300
        });
        return res.json('data');
    });
}

/**
 * Creates a new account
 * @param body The account creation request body
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function createAccount(body: any, customHeaders: CustomHeaders) {
    return group("Create Account", () => {
        const res = globalThis.api.post("/v1/accounts", body, "create account", customHeaders);
        check(res, {
            "Create account successful": (r) => r.status >= 200 && r.status < 300
        });
        return res.json('data');
    });
}

/**
 * Downloads annex document for a specific account
 * @param accountId The ID of the account
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getDownloadAnnexForAccounts(accountId: string, customHeaders: CustomHeaders) {
    return group("Download Annex for Account", () => {
        const res = globalThis.api.get(`/v1/accounts/annex/${accountId}`, "download annex for account", customHeaders);
        check(res, {
            "Download annex for account successful": (r) => r.status >= 200 && r.status < 300
        });
        return res;
    });
}

/**
 * Validates if user can open a new account
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getValidateOpenNewAccount(customHeaders: CustomHeaders) {
    return group("Validate Open New Account", () => {
        const res = globalThis.api.get("/v1/accounts-catalog", "validate open new account", customHeaders);
        check(res, {
            "Validate open new account successful": (r) => r.status >= 200 && r.status < 300
        });
        return res.json('data');
    });
}

/**
 * Gets the accounts catalog
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getAccountsCatalog(customHeaders: CustomHeaders) {
    return group("Get Accounts Catalog", () => {
        const res = globalThis.api.get("/v1/accounts-catalog", "get accounts catalog", customHeaders);
        check(res, {
            "Get accounts catalog successful": (r) => r.status >= 200 && r.status < 300
        });
        return res;
    });
}

/**
 * Gets the list of accounts
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getAccounts(customHeaders: CustomHeaders) {
    return group("Get Accounts", () => {
        const res = globalThis.api.get("/v1/accounts", "get accounts", customHeaders);
        check(res, {
            "Get accounts successful": (r) => r.status >= 200 && r.status < 300
        });
        return res;
    });
}

