import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function listCreditCards(customHeaders: CustomHeaders) {
  return group("List cards", () => {
    const res = api.get("/v1/cards", "listCreditCards", customHeaders);

    check(res, {
      "listCreditCards response is success": (r) => r.status === 200,
    });
    if (res.status !== 200) {
      throw new Error(
        `Use Case Failure - list credit cards failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
    try {
      return res.json("data.cards");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return cards. - ${e.message}`,
      );
    }
  });
}

export function listCreditSupplementaryCards(customHeaders: CustomHeaders) {
  return group("List Supplementary cards", () => {
    const res = api.get(
      "/v1/cards?includesupplementary=false",
      "listSupplementaryCreditCards",
      customHeaders,
    );

    check(res, {
      "listSupplementaryCreditCards response is success": (r) =>
        r.status === 200,
    });
    try {
      return res.json("data.balanceLastUpdatedcards");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return last balance. - ${e.message}`,
      );
    }
  });
}
