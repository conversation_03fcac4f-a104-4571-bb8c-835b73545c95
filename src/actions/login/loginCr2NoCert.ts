import { group, check, fail } from "k6";
import exec from "k6/execution";
import { parseHTML } from "k6/html";

export function loginCr2NoCert(
  username: string,
  password: string,
): string | undefined {
  return group("Login with username and password", () => {
    const payload: { [key: string]: string } = {
      username: username,
      userpassphrase: password,
      defaultlocaleid: "en",
    };
    const response = idp.cr2DirectRequest(
      "/bml/authentication/login_loginnocert.bml",
      payload,
      "login",
    );

    const doc = parseHTML(response.body as string);
    const successValue = doc.find("success success").text();

    check(response, {
      "login response is success": (r) =>
        r.status === 200 && successValue === "1",
    });

    const cue123 = doc.find("success csrftoken").text();
    if (!cue123) {
      const user = globalThis.users[exec.vu.idInTest - 1];
    } else {
      const parts = cue123.split("=");
      const csrf = parts[1];
      return csrf;
    }
  });
}
