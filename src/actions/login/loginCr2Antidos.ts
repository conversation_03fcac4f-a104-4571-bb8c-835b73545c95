import { group, check } from "k6";
import { parseHTML } from "k6/html";

export function loginCr2Anti<PERSON>(username: string, password: string): string {
  const payload: { [key: string]: string } = {
    username: username,
    userpassphrase: password,
    bosauthenticationtoken: "46r5h",
    tokentype: "BOS_GENERIC_TOKEN_2",
    challenge_type: "antidos",
    BOS_GENERIC_TOKEN:
      "BOS_AUTHENTICATION,<data><ip>**********</ip><finger_print_hash>a82eefe38da63ce229b8956141f96f1c32fc4535e9c1e99a559326ba8ff6aeaa</finger_print_hash><finger_print_raw>eyJicm93c2VyIjp7InVzZXJBZ2VudCI6Ik1vemlsbGEvNS4wIChNYWNpbnRvc2g7IEludGVsIE1hYyBPUyBYIDEwXzE1XzcpIEFwcGxlV2ViS2l0LzYwNS4xLjE1IChLSFRNTCwgbGlrZSBHZWNrbykgVmVyc2lvbi8xNy4xIFNhZmFyaS82MDUuMS4xNSIsImFwcGxpY2F0aW9uVmVyc2lvbiI6IjUuMCAoTWFjaW50b3NoOyBJbnRlbCBNYWMgT1MgWCAxMF8xNV83KSBBcHBsZVdlYktpdC82MDUuMS4xNSAoS0hUTUwsIGxpa2UgR2Vja28pIFZlcnNpb24vMTcuMSBTYWZhcmkvNjA1LjEuMTUiLCJhcHBsaWNhdGlvbkNvZGUiOiJNb3ppbGxhIiwiYXBwbGljYXRpb25OYW1lIjoiTmV0c2NhcGUiLCJjb29raWVFbmFibGVkIjp0cnVlLCJqYXZhRW5hYmxlZCI6ZmFsc2V9LCJzdXBwb3J0Ijp7ImFqYXgiOnRydWUsImNoZWNrQ2xvbmUiOnRydWUsImNoZWNrT24iOnRydWUsImNvcnMiOnRydWUsIm5vQ2xvbmVDaGVja2VkIjp0cnVlfSwiZGV2aWNlIjp7InNjcmVlbldpZHRoIjoxNzI4LCJzY3JlZW5IZWlnaHQiOjExMTcsIm9zIjoiQXBwbGUgTWFjT1MiLCJsYW5ndWFnZSI6ImVuLUdCIiwicGxhdGVmb3JtIjoiTWFjSW50ZWwiLCJ0aW1lWm9uZSI6LTEyMH0sInBsdWdpbiI6W3sibmFtZSI6IlBERiBWaWV3ZXIiLCJmaWxlIjoiaW50ZXJuYWwtcGRmLXZpZXdlciIsImRlc2NyaXB0aW9uIjoiUG9ydGFibGUgRG9jdW1lbnQgRm9ybWF0In0seyJuYW1lIjoiQ2hyb21lIFBERiBWaWV3ZXIiLCJmaWxlIjoiaW50ZXJuYWwtcGRmLXZpZXdlciIsImRlc2NyaXB0aW9uIjoiUG9ydGFibGUgRG9jdW1lbnQgRm9ybWF0In0seyJuYW1lIjoiQ2hyb21pdW0gUERGIFZpZXdlciIsImZpbGUiOiJpbnRlcm5hbC1wZGYtdmlld2VyIiwiZGVzY3JpcHRpb24iOiJQb3J0YWJsZSBEb2N1bWVudCBGb3JtYXQifSx7Im5hbWUiOiJNaWNyb3NvZnQgRWRnZSBQREYgVmlld2VyIiwiZmlsZSI6ImludGVybmFsLXBkZi12aWV3ZXIiLCJkZXNjcmlwdGlvbiI6IlBvcnRhYmxlIERvY3VtZW50IEZvcm1hdCJ9LHsibmFtZSI6IldlYktpdCBidWlsdC1pbiBQREYiLCJmaWxlIjoiaW50ZXJuYWwtcGRmLXZpZXdlciIsImRlc2NyaXB0aW9uIjoiUG9ydGFibGUgRG9jdW1lbnQgRm9ybWF0In1dfQ==</finger_print_raw></data>",
    defaultlocaleid: "en",
    CUE123: "Bp2ewi6B6ZmDqEeCGfyL1FBcRey8DYLHuFA6TGSTVxY.",
  };
  return group("Login with username and password", () => {
    const response = idp.cr2DirectRequest(
      "/bml/authentication/login_loginantidos.bml",
      payload,
      "login",
    );

    if (typeof response.body === "string") {
      const doc = parseHTML(response.body);
      const successValue = doc.find("success success").text();
      check(response, {
        "is status 200 and login successful": (r) =>
          r.status === 200 && successValue === "1",
      });
    }

    return "";
  });
}
