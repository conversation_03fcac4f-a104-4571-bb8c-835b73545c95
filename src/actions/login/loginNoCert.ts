import { group, check } from "k6";

// Function for logging in without a certificate
export function loginNoCert(username: string, password: string): string {
  return group("Login with username and password", () => {
    // Initial login request without certificate
    let returnToken: string | undefined;
    const response = idp.post(
      `/v1/auth/login/perf/nocert`,
      {
        username,
        password,
      },
      "login", // This is the label (string)
    );

    // Handle status 428: Requires confirmation (e.g., two-factor authentication)
    if (response.status === 428) {
      const token = response.json("token")?.toString();
      if (token) {
        // Confirm login with the provided token
        const tokenresponse = idp.post(
          `/v1/auth/login`,
          {
            username,
            password,
            headers: {
              Authorization: `Bearer ${token}`, // Add token here in the payload if supported
            },
          },
          "Confirm login", // This is the label (string)
        );
        returnToken = tokenresponse.json("data.access_token")?.toString();
      }
    }

    // Handle status 417: Password reset required
    if (response.status === 417) {
      const token = response.json("token")?.toString();
      if (token) {
        // Perform the password reset with the required headers and payload
        const resetPasswordPayload = {
          userName: username,
          newPassword: "NewPassword@202", // Define the new password here
          oldPassword: password,
          headers: {
            Authorization: `Bearer ${token}`, // Add token here if supported
            "Content-Type": "application/json", // Ensure the content type is JSON
          },
        };

        const resetresponse = idp.post(
          `/v1/auth/reset-expired-password`,
          resetPasswordPayload,
          "Reset expired password", // This is the label (string)
        );

        // Log the reset password response message (optional)
        const resetMessage = resetresponse.json("message")?.toString();
        console.log(resetMessage); // Should log "password reset successfully."

        if (resetMessage === "password reset successfully.") {
          // After password reset, login with the new password
          const newPassword = resetPasswordPayload.newPassword;
          const resetLoginesponse = idp.post(
            `/v1/auth/login/perf/nocert`,
            {
              username,
              password: newPassword, // Login with the new password
            },
            "login", // This is the label (string)
          );
          returnToken = resetLoginesponse.json("data.access_token")?.toString();
        }
      }
    }

    // Check if the final response status is 200 (successful login)
    check(response, {
      "is status 200": (r) => r.status === 200,
    });

    // Return the access token if login was successful, otherwise return an empty string
    return returnToken || "";
  });
}
