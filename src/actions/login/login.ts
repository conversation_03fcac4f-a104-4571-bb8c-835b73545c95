import { group, check } from "k6";
import { RefinedResponse } from "k6/http";
import {loginWithBiometricPayload} from "../../core/httpx";

export function login(username: string, password: string, deviceId: string): string | undefined {
  return group("Login with username and password", () => {
    const validationResponse = idp.get(
      "/v3/auth/login/validation-rules",
      "validation-rules",
    );
    check(validationResponse, {
      "validation rules response is success": (r) => r.status === 200,
    });

    const tutorialResponse = api.get(
      "/v1/cms/registration/tutorial",
      "tutorial",
    );
    check(tutorialResponse, {
      "tutorial response is success": (r) => r.status === 200,
    });
    
    let returnToken: string | undefined;
    let loginResponse: RefinedResponse<any>;

    loginResponse = idp.post(
      `/v3/auth/login`,
      {
        username,
        password: globalThis.passwordWasReset? globalThis.resetPassword: password,
      },
      "login",
      {deviceId}
    );

    check(loginResponse, {
      "original login response is success": (r) => r.status === 200,
    });

    switch (loginResponse.status) {
      case 400:
        throw new Error(
          `Bad Request - response status code: ${loginResponse.status}`,
        );
      case 401:
        throw new Error(
          `Authentication Failure - response status code: ${loginResponse.status}`,
        );
      case 428:
        try {
          const token = loginResponse.json("token")?.toString();
          loginResponse = idp.post(
            `/v3/auth/login`,
            {
              username,
              password: globalThis.passwordWasReset? globalThis.resetPassword: password,
            },
            "Confirm login",
            {token,deviceId},
          );
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
            `Authentication Failure - response json does not return token. - ${e.message}`,
          );
        }
        break;
      case 417:

        const token = loginResponse.json("token")?.toString();
        if (token) {
          // Perform the password reset with the required headers and payload
          const resetPasswordPayload = {
            userName: username,
            newPassword: globalThis.resetPassword, // Define the new password here
            oldPassword: password,
          };

          const expiredResponse = idp.post(
            `/v3/auth/reset-expired-password`,
            resetPasswordPayload,
            "Reset expired password", // This is the label (string)
            {token}
          );

          check(expiredResponse, {
            "reset password response is success": (r) => r.status === 200,
          });

          // Log the reset password response message (optional)
          const resetMessage = expiredResponse.json("message")?.toString();
          console.log(resetMessage); // Should log "password reset successfully."

          if (resetMessage === "password reset successfully.") {
            globalThis.passwordWasReset = true;
            // After password reset, login with the new password
            const newPassword = resetPasswordPayload.newPassword;
            loginResponse= idp.post(
              `/v3/auth/login`,
              {
                username,
                password: newPassword, // Login with the new password
              },
              "login", // This is the label (string)
              {deviceId}
            );

            check(loginResponse, {
              "new password login response is success": (r) => r.status === 200,
            });

            returnToken = loginResponse
              ?.json("data.access_token")
              ?.toString();
          }
        }
        break;
      case 500:
        throw new Error(
          `Internal Server Error - response status code: ${loginResponse.status}`,
        );
      case 503:
        throw new Error(
          `Service Unavailable - response status code: ${loginResponse.status}`,
        );
      default:
        try {
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
            `Authentication Failure - response json does not return access token. - ${e.message}`,
          );
        }
    }

    try {
      if (!returnToken) {
      } else {
        returnToken = handleTrustDevice(loginResponse, username, returnToken, deviceId);
        if (!returnToken) {
        } else {
          return returnToken;
        }
      }
    } catch (e: any) {
      throw new Error(
        `Authentication Failure - response json does not return access token. - ${e.message}`,
      );
    }
  });
}

export function loginWithBiometric(payload : loginWithBiometricPayload): string | undefined {
  return group("Login with username and password", () => {
    const validationResponse = idp.get(
        "/v3/auth/login/validation-rules",
        "validation-rules",
    );
    check(validationResponse, {
      "validation rules response is success": (r) => r.status === 200,
    });

    const tutorialResponse = api.get(
        "/v1/cms/registration/tutorial",
        "tutorial",
    );
    check(tutorialResponse, {
      "tutorial response is success": (r) => r.status === 200,
    });

    let returnToken: string | undefined;
    let loginResponse: RefinedResponse<any>;
    const loginPayload = payload.data;

    loginResponse = idp.post(
        `/v3/auth/biometric/login`,
        loginPayload,
        "login",
        {
            "app-version":
                "MIICAQYJKoZIhvcNAQcDoIIB8jCCAe4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQA+Sk26YUuRrcmdDk3499493cDiiKmvIabQIdCW7rnp8enT0ZZenYO3X3u41eFP+Tl400nN258/EMp4v2PjCD+gZRMDnc8y8ODI3v0emPt4Fr6J4EMYWhWZppfpF4XV4PhyKgxhjezoHv85WP+OwO9JPLrlOMlUrlHGHWlcMyKJSzShiz14DOvma+u86wYZ5Lrw+9O+wUffVt67/3VH2WDdlw4DuYAreqkRGd0ZiaLBwNr+jyrl7tgO4Efkrcxi6mcY3R7Sc0nm5gvE4ACDlTdASYHyKppExwTF5UAweVEeD4v5BvEObY34trqWynjdCypm4Ka4ykd220NG3Vsrb/FQMD4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEIE1avqTqsoQIp48H/+wMqqgEgQQzjtEMVtOC1ZEfoi8KBP3Mg==",
            "Content-Type": "application/json",
            login_auth_key: "dds",
            "device-id": payload.data.deviceId,
        }

    );

    check(loginResponse, {
      "original login response is success": (r) => r.status === 200,
    });

    switch (loginResponse.status) {
      case 400:
        throw new Error(
            `Bad Request - response status code: ${loginResponse.status}`,
        );
      case 401:
        throw new Error(
            `Authentication Failure - response status code: ${loginResponse.status}`,
        );
      case 428:
        try {
          const token = loginResponse.json("token")?.toString();
          loginResponse = idp.post(
              `/v3/auth/biometric/login`,
              loginPayload,
              "Confirm login",
              {token},
          );
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return token. - ${e.message}`,
          );
        }
        break;
      case 500:
        throw new Error(
            `Internal Server Error - response status code: ${loginResponse.status}`,
        );
      case 503:
        throw new Error(
            `Service Unavailable - response status code: ${loginResponse.status}`,
        );
      default:
        try {
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return access token. - ${e.message}`,
          );
        }
    }

    try {
      if (!returnToken) {
      } else {
        returnToken = handleTrustDevice(loginResponse, payload.user, returnToken, payload.data.deviceId);
        if (!returnToken) {
        } else {
          return returnToken;
        }
      }
    } catch (e: any) {
      throw new Error(
          `Authentication Failure - response json does not return access token. - ${e.message}`,
      );
    }
  });
}

function handleTrustDevice(loginResponse: RefinedResponse<any>, username: string, token: string | undefined, deviceId: string): string | undefined {
  const isTrusted = loginResponse?.json("data.isTrusted")?.valueOf() as boolean;
  if(!isTrusted){
    const trustResponseNoOtp = idp.post(
      `/v3/auth/trust`,
      {
        // deviceId: `${username}_deviceId`,
        deviceId : deviceId
      },
      "trustDevice",
      {token},
    );

    check(trustResponseNoOtp, {
      "trust response without OTP is success": (r) => r.status === 428,
    });

    const trustResponseOtp = idp.post(
      `/v3/auth/trust`,
      {
        deviceId: deviceId,
        otp: '555555'
      },
      "trustDevice",
      {token},
    );

    check(trustResponseOtp, {
      "trust response with OTP is success": (r) => r.status === 200,
    });

    return trustResponseOtp?.json("accessToken")?.toString();
  } else {
    return token;
  }
}
