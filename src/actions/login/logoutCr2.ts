import { group, check, fail } from "k6";
import { parseHTML } from "k6/html";

export function logoutCr2(token: string) {
  return group("logout user", () => {
    const payload: { [key: string]: string } = {
      CUE123: token,
    };
    const response = idp.cr2DirectRequest(
      "/bml/authentication/logout.bml",
      payload,
      "logout",
    );

    const doc = parseHTML(response.body as string);
    const successValue = doc.find("success success").text();
    check(response, {
      "logout response is success": (r) =>
        r.status === 200 && successValue === "1",
    });
  });
}
