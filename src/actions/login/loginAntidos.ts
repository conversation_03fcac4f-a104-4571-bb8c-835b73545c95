import { group, check } from "k6";

export function loginAnti<PERSON>(username: string, password: string): string {
  return group("Login with username and password", () => {
    const response = idp.post(
      `/v1/auth/login/perf/antidos`,
      {
        username,
        password,
      },
      "login",
    );
    check(response, {
      "login is good": (r) => r.status === 200,
    });
    if (response.status == 428) {
      const token = response.json("token")?.toString();

      const secondresponse = idp.post(
        `/v1/auth/login`,
        {
          username,
          password,
        },
        "Confirm login",
        {token},
      );
      check(secondresponse, {
        "second login is status 200": (r) => r.status === 200,
      });

      return secondresponse.json("data.access_token")?.toString() || "";
    }

    return response.json("data.access_token")?.toString() || "";
  });
}
