export function getEligibleLocalTransferAccount(accountList: [any]): any {
  return accountList.find(
    (el) =>
      el.operations.createTransfer.enabled === true &&
      el.availableHomeBalance > 20,
  );
}

export function getEligibleBookDepositAccount(accountList: [any]): any {
  return accountList.find(
    (el) =>
      el.operations.deposit.enabled === true && el.availableBalance > 10000,
  );
}

export function getEligibleForBillPayment(accountList: [any]): any {
  return accountList.find(
    (el) =>
      el.operations.createTransfer.enabled === true &&
      el.availableBalance > 1000,
  );
}
