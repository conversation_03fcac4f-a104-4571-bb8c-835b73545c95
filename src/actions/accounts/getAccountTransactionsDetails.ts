import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function getAccountTransactionsDetails(
  transactionID: any,
  customHeaders: CustomHeaders,
) {
  return group("Get transaction details", () => {
    const body = {
      id: transactionID,
      type: "history",
    };
    const res = api.post(
      "/v1/transfers-per/transfers-per/details",
      body,
      "getAccountTransactionsDetails",
      customHeaders,
    );
    check(res, {
      "get account transaction details success": (r) => r.status === 200,
    });

    if (res.status !== 200) {
      throw new Error(
        `Use Case Failure - get account transaction details failed. - ${res.status} - ${res.body} - ${res.request.url} - ${transactionID} - ${customHeaders.token}`,
      );
    }

    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message} - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
  });
}
