import { group, check } from "k6";
import { parseHTML } from "k6/html";

export function getProductListCr2(csrf: string) {
  group("Get product list", () => {
    const payload: { [key: string]: string } = {
      CUE123: csrf,
    };
    const response = idp.cr2DirectRequest(
      "/bml/account/accountsCustomer.bml",
      payload,
      "accountsCustomer",
    );

    const doc = parseHTML(response.body as string);
    const products = doc.find("response collection model name").text();
    check(response, {
      "accountsCustomer response is success": (r) =>
        r.status === 200 && products !== "",
    });

  });
}
