import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function listAccounts(customHeaders: CustomHeaders) {
  return group("List accounts", () => {
    const res = api.get("/v1/accounts", "listAccounts", customHeaders);
    check(res, {
      "get list accounts success": (r) => r.status === 200,
    });

    if (res.status !== 200) {
      throw new Error(
        `Use Case Failure - list accounts failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function listAccountsForLocalTransfer(customHeaders: CustomHeaders) {
  return group("List accounts for local transfer", () => {
    const res = api.get(
      "/v1/accounts?transferType=local",
      "listAccountsLocalTransfer",
      customHeaders,
    );
    check(res, {
      "list accounts local transfer success": (r) => r.status === 200,
    });
    try {
      return res.json("data.accounts");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return accounts. - ${e.message}`,
      );
    }
  });
}

export function listAccountsForBookDeposits(customHeaders: CustomHeaders) {
  return group("List accounts for book deposit", () => {
    const res = api.get(
      "/v1/accounts?currency=EGP&transferTo=true&requestType=deposits&minAmount=1000",
      "listAccountsBookDeposits",
      customHeaders,
    );
    check(res, {
      "list accounts for book deposits": (r) => r.status === 200,
    });
    try {
      return res.json("data.accounts");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return accounts. - ${e.message}`,
      );
    }
  });
}

export function listAccountsForCardsTransfer(customHeaders: CustomHeaders) {
  return group("List accounts for credit card transfer", () => {
    const res = api.get(
      "/v1/accounts?transferType=card",
      "listAccountsCreditCardTransfer",
      customHeaders,
    );
    check(res, {
      "list accounts local transfer success": (r) => r.status === 200,
    });
    try {
      return res.json("data.accounts");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return accounts. - ${e.message}`,
      );
    }
  });
}
export function listAccountsForBillPayment(customHeaders: CustomHeaders) {
  return group("List accounts for bill payment", () => {
    const res = api.get(
      "/v1/accounts?transferType=local",
      "listAccountsBillPayments",
      customHeaders,
    );
    check(res, {
      "list accounts for bill payment success": (r) => r.status === 200,
    });
    try {
      return res.json("data.accounts");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return accounts. - ${e.message}`,
      );
    }
  });
}
