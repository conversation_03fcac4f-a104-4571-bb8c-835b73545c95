import { group, check } from "k6";
import exec from "k6/execution";
import { CustomHeaders } from "../../core/httpx";

export function getProductList(customHeaders: CustomHeaders) {
  group("Get product list", () => {
    const res = api.get(
      "/v1/accounts/home/<USER>",
      "getProductList",
      customHeaders,
    );

    check(res, {
      "is status 200": (r) => r.status === 200,
    });

    if (res.status === 503) {
      const index = exec.vu.idInTest - 1;
      const user = globalThis.users[index];
    }
  });
}
