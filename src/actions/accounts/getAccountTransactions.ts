import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function getAccountDetails(account: any, customHeaders: CustomHeaders) {
  return group("Get account details", () => {
    const body = {
      accountNumber: account,
    };
    const res = api.post(
      "/v1/accounts-per/account",
      body,
      "getAccountDetails",
      customHeaders,
    );
    check(res, {
      "get account details success": (r) => r.status === 200,
    });
    try {
      const id = res.json("data.id")?.toString();
      return id;
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return id. - ${e.message}`,
      );
    }
  });
}

export function getAccountTransactions(accountId: any, customHeaders: CustomHeaders) {
  return group("Get account transactions", () => {
    const body = {
      accountId: accountId,
      type: "recent",
      currency: "EGP",
    };
    const res = api.post(
      "/v1/transfers-per/transactions",
      body,
      "getAccountTransactions",
      customHeaders,
    );
    check(res, {
      "get account transactions success": (r) => r.status === 200,
    });

    if (res.status !== 200) {
      throw new Error(
        `Use Case Failure - account transfers fetch failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getHistoryAccountTransactions(accountId: any, customHeaders: CustomHeaders) {
  return group("Get account transactions", () => {
    const body = {
      accountId: accountId,
      type: "history",
      currency: "EGP",
      fromDate: "10/10/2020",
      toDate: "10/10/2024",
      fromAmount: 1,
      toAmount: 1000,
      sort: 'asc'
    };
    const res = api.post(
      "/v1/transfers-per/transactions",
      body,
      "getAccountTransactions",
      customHeaders,
    );
    check(res, {
      "get account transactions success": (r) => r.status === 200,
    });

    if (res.status !== 200) {
      throw new Error(
        `Use Case Failure - account transfers fetch failed. - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}
