import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

/**
 * Performs an IPN transfer using the provided request body and headers
 * @param body The transfer request body
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function performIpnTransfer(body: any, customHeaders: CustomHeaders) {
  return group("IPN Transfer", () => {
    const res = api.post(
      "/v1/thub/ipn/transfers",
      body,
      "perform ipn transfer",
      customHeaders,
    );

    check(res, {
      "IPN transfer request successful": (r) => r.status === 201,
    });

    return res;
  });
}

/**
 * Gets the list of all IPN transfers for the authenticated user
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getIpnTransfersList(customHeaders: CustomHeaders) {
  return group("Get IPN Transfers List", () => {
    const res = api.get(
      "/v1/thub/ipn/transfers",
      "get ipn transfers list",
      customHeaders,
    );
    
    check(res, {
      "Get IPN transfers list successful": (r) => r.status === 200,
    });
    
    return res;
  });
}

/**
 * Gets a specific IPN transfer by its global ID
 * @param globalId The global ID of the transfer
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getIpnTransferById(globalId: string, customHeaders: CustomHeaders) {
  return group("Get IPN Transfer By ID", () => {
    const res = api.get(
      `/v1/thub/ipn/transfers/${globalId}`,
      "get ipn transfer by id",
      customHeaders,
    );
    
    check(res, {
      "Get IPN transfer by ID successful": (r) => r.status === 200,
    });
    
    return res;
  });
}

/**
 * Simulates the fees for an IPN transfer
 * @param body The transfer request body
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function simulateIpnTransferFees(body: any, customHeaders: CustomHeaders) {
  return group("simulate transfer fees", () => {
    const res = api.post("/v1/thub/ipn/transfers/fees", body, "simulate transfer fees", customHeaders)
    check(res, {
      "simulate fees success": (r) => r.status === 201,
    })

    return res.json()
  })
}

/**
 * Gets the list of banks available for IPN transfers
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getBanksList(customHeaders: CustomHeaders) {
  return group("Get Banks List", () => {
    const res = api.get(
      "/v1/thub/ipn/banks",
      "get banks list",
      customHeaders,
    );
    
    check(res, {
      "Get banks list successful": (r) => r.status === 200,
    });
    
    return res;
  });
}

/**
 * Gets the list of transfer reasons
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getTransferReasons(customHeaders: CustomHeaders) {
  return group("Get Transfer Reasons", () => {
    const res = api.get(
      "/v1/thub/ipn/transfer-reasons",
      "get transfer reasons",
      customHeaders,
    );
    
    check(res, {
      "Get transfer reasons successful": (r) => r.status === 200,
    });
    
    return res;
  });
}

/**
 * Gets the list of IPN beneficiaries
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getIpnBeneficiaries(customHeaders: CustomHeaders) {
  return group("Get IPN Beneficiaries", () => {
    const res = api.get(
      "/v1/thub/ipn/beneficiaries",
      "get ipn beneficiaries",
      customHeaders,
    );
    
    check(res, {
      "Get IPN beneficiaries successful": (r) => r.status === 200,
    });
    return res.json('data');
  });
}

/**
 * Gets a specific IPN beneficiary by ID
 * @param beneficiaryId The ID of the beneficiary
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getIpnBeneficiaryById(beneficiaryId: string, customHeaders: CustomHeaders) {
  return group("Get IPN Beneficiary By ID", () => {
    const res = api.get(
      `/v1/thub/ipn/beneficiaries/${beneficiaryId}`,
      "get ipn beneficiary by id",
      customHeaders,
    );
    
    check(res, {
      "Get IPN beneficiary by ID successful": (r) => r.status === 200,
    });
    return res;
  });
}

/**
 * Creates a new IPN beneficiary
 * @param body The beneficiary request body
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function createIpnBeneficiary(body: any, customHeaders: CustomHeaders) {
  return group("Create IPN Beneficiary", () => {
    const res = api.post(
      "/v1/thub/ipn/beneficiaries",
      body,
      "create ipn beneficiary",
      customHeaders,
    );

    
    check(res, {
      "Create IPN beneficiary successful": (r) => r.status === 201,
    });

    return res;
  });
}


/**
 * Deletes an IPN beneficiary
 * @param beneficiaryId The ID of the beneficiary to delete
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function deleteIpnBeneficiary(beneficiaryId: string, customHeaders: CustomHeaders) {
  return group("Delete IPN Beneficiary", () => {
    const res = api.delete(
      `/v1/thub/ipn/beneficiaries/${beneficiaryId}`,
      "delete ipn beneficiary",
      customHeaders,
    );
    
    check(res, {
      "Delete IPN beneficiary successful": (r) => r.status === 200,
    });
    return res;
  });
}

/**
 * Gets the list of IPN categories
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getIpnCategories(customHeaders: CustomHeaders) {
  return group("Get IPN Categories", () => {
    const res = api.get(
      "/v1/thub/ipn/categories",
      "get ipn categories",
      customHeaders,
    );
    
    check(res, {
      "Get IPN categories successful": (r) => r.status === 200,
    });
    
    return res;
  });
}
/**
 * Calling Web Hook
 * @param transferID
 * @param body
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function callWebHook(transferID : string,body : any, customHeaders: CustomHeaders) {
  return group("Calling Web Hook", () => {
    const res = api.post(
        `/v1/thub/ipn/transfers/${transferID}`,
        body,
        "Call web Hook",
        customHeaders
    );

    check(res, {
      "Web hook Called  successful": (r) => r.status === 201,
    });
    return res;
  });
}

/**
 * Gets the IPN transfer limits
 * @param customHeaders Headers containing authentication token and device ID
 * @returns The response object
 */
export function getIpnTransferLimits(customHeaders: CustomHeaders) {
  return group("Get IPN Transfer Limits", () => {
    const res = api.get(
      "/v1/thub/ipn/limits",
      "get ipn transfer limits",
      customHeaders,
    );
    
    check(res, {
      "Get IPN transfer limits successful": (r) => r.status === 201,
    });
    
    return res;
  });
}

