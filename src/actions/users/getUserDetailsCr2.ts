import { group, check, fail } from "k6";
import { parseHTML } from "k6/html";

export function getUserDetailsCr2(csrf: string) {
  return group("Getting the user details", () => {
    getUser(csrf);
    getExtraUser(csrf);
    getNotifications(csrf);
    getActiveNotifications(csrf);
  });
}

function getUser(csrf: string) {
  const payload: { [key: string]: string } = {
    CUE123: csrf,
  };
  const response = idp.cr2DirectRequest("/bml/misc/user.bml", payload, "user");

  const doc = parseHTML(response.body as string);
  const customername = doc.find("response user customername").text();
  check(response, {
    "user response is success": (r) => r.status === 200 && customername !== "",
  });
}

function getExtraUser(csrf: string) {
  const payload: { [key: string]: string } = {
    CUE123: csrf,
  };
  const response = idp.cr2DirectRequest(
    "/bml/misc/externaluser.bml",
    payload,
    "externaluser",
  );

  const doc = parseHTML(response.body as string);
  const text4 = doc.find("response user text4").text();
  check(response, {
    "externaluser response is success": (r) => r.status === 200 && text4 !== "",
  });
}

function getNotifications(csrf: string) {
  const payload: { [key: string]: string } = {
    CUE123: csrf,
  };
  const response = idp.cr2DirectRequest(
    "/bml/notifications/notif-new.bml",
    payload,
    "notifications",
  );

  const doc = parseHTML(response.body as string);
  const customername = doc.find("resonse messages").text();
  check(response, {
    "notifications response is success": (r) =>
      r.status === 200 && customername !== "",
  });
}

function getActiveNotifications(csrf: string) {
  const payload: { [key: string]: string } = {
    CUE123: csrf,
  };
  const response = idp.cr2DirectRequest(
    "/bml/notifications/getactivenotifications.bml",
    payload,
    "activenotifications",
  );

  const doc = parseHTML(response.body as string);
  check(response, {
    "activenotifications response is success": (r) => r.status === 200,
  });
}
