import { setupVu } from "./core/setup";
import {
  accessHomePageExec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageCr2Exec,
  createTransferInsideCIB,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userTransferInsideCib: createTransferInsideCIB("load"),
  },
};

export {
  accessHomePageExec,
  accessHomePageCr2Exec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
};
