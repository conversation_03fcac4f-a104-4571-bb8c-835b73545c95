import { setupVu } from "./core/setup";
import { ipnTransfer, ipnTransferExec } from "./scenarios";

// Initialize the test environment
setupVu();

// Configure global options for the load test
export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userIpnTransfer: ipnTransfer("load"),
  },
};

// Export the execution function for direct use
export { ipnTransferExec };