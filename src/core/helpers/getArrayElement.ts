import { maxVirtualUsers } from "../loadSetup";

function selectArrayElement<T>(vuId: number, array: T[]): T {
  if (array.length === 0) {
    throw new Error("Array is empty");
  }

  const maxVus = maxVirtualUsers;

  if (maxVus <= 0) {
    throw new Error("maxVirtualUsers must be greater than 0");
  }

  if (maxVus >= array.length) {
    return array[vuId % array.length];
  }

  const scaledIndex = Math.min(
    Math.floor((vuId / maxVus) * array.length),
    array.length - 1,
  );

  return array[scaledIndex];
}

export const selectUser = selectArrayElement;
export const selectAccount = selectArrayElement;
