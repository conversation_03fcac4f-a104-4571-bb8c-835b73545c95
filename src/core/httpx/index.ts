import http from "k6/http";

export type CustomHeaders = {
  token?: string
  deviceId?: string
  login_auth_key? : string
  'app-version'? : string
}
export type loginWithBiometricPayload =  {
  "user": string
  "data": {
    "deviceId": string
    "payload": {
      cipherKey: string
      kcUserId: string
      deviceId: string
    }
    signature : string
    cipherKey: string
    kcUserId: string
    "nonce": string
  }
}
export class Httpx {
  private baseURL: string;
  private cr2BaseUrl: string;

  constructor(opts: { baseUrl: string }) {
    this.baseURL = opts.baseUrl;
    this.cr2BaseUrl = "https://***********/CIBInternet240";
  }

  private request(
    method: "POST" | "GET" | "PATCH" | "DELETE",
    path: string,
    operationId: string,
    customHeaders?: CustomHeaders,
    body?: any,
  ) {
    const url = `${this.baseURL}${path}`;
    const headers = {
      ...(customHeaders?.token ? { authorization: `Bearer ${customHeaders.token}` } : {}),
      ...(customHeaders?.deviceId ? { 'device-id': customHeaders.deviceId } : {}),
      "Content-Type": "application/json",
      login_auth_key: "mobile",
      ...customHeaders
    };
    const resp = http.request(method, url, JSON.stringify(body), {
      headers,
      tags: { operationId },
    });
    return resp;
  }

  public cr2DirectRequest(
    path: string,
    payload: { [key: string]: string },
    operationId: string,
  ) {
    const url = `${this.cr2BaseUrl}${path}`;

    const headers = {
      "Content-Type": "application/json",
    };

    const formdata = Object.keys(payload)
      .map(
        (key) =>
          encodeURIComponent(key) + "=" + encodeURIComponent(payload[key]),
      )
      .join("&");

    const resp = http.request("POST", url, formdata, {
      headers,
      tags: { operationId },
    });

    return resp;
  }

  public post(path: string, body: any, operationId: string, customHeaders?: CustomHeaders | any) {
    return this.request("POST", path, operationId, customHeaders, body);
  }

  public get(path: string, operationId: string, customHeaders?: CustomHeaders) {
    return this.request("GET", path, operationId, customHeaders);
  }

  public patch(path: string, body: any, operationId: string, customHeaders?: CustomHeaders) {
    return this.request("PATCH", path, operationId, customHeaders, body);
  }

  public delete(path: string, operationId: string, customHeaders?: CustomHeaders) {
    return this.request("DELETE", path, operationId, customHeaders);
  }
}
