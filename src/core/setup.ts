import { SharedArray } from "k6/data";
import { Httpx } from "./httpx";
import { loadArray, virtualUsers } from "./loadSetup";

const environment = __ENV.ENVIRONMENT || "per";
const idpBaseUrl =
  __ENV.IDP_BASE_URL ||
  `https://idp-${environment}.apps.ocp-nonprod-02.hodomain.local`;
const apiBaseUrl =
  __ENV.API_BASE_URL ||
  `https://api-${environment}.apps.ocp-nonprod-02.hodomain.local`;

globalThis.users = new SharedArray("clients", function () {
  return JSON.parse(open(`../data/clients.${environment}.json`));
});

globalThis.BiometricLoginUsers = new SharedArray("biometric-clients", function () {
    return JSON.parse(open(`../data/clients.${environment}.bio.json`));
});

globalThis.accountsSubUsers = new SharedArray("clients-accounts", function () {
  return JSON.parse(open(`../data/clients.${environment}.accounts.json`));
});
globalThis.accountsTransactionsSubUsers = new SharedArray(
  "clients-accounts-Transactions-Details",
  function () {
    return JSON.parse(
      open(`../data/clients.${environment}.accountsTransactiosn.json`),
    ).map((user: any) => ({ ...user, token: "" }));
  },
);

globalThis.billPaymentSubUsers = new SharedArray(
  "clients-accounts-Bill-Payment",
  function () {
    return JSON.parse(open(`../data/clients.${environment}.BP.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.transfersInsideCibSubUsers = new SharedArray(
  "clients-transfers-inside-cib",
  function () {
    return JSON.parse(
      open(`../data/clients.${environment}.transfersInsideCib.json`),
    ).map((user: any) => ({ ...user, token: "" }));
  },
);

globalThis.transfersOutsideCibSubUsers = new SharedArray(
  "clients-transfers-outside-cib",
  function () {
    return JSON.parse(
      open(`../data/clients.${environment}.transfersOutsideCib.json`),
    ).map((user: any) => ({ ...user, token: "" }));
  },
);

globalThis.bookDepositsSubUsers = new SharedArray(
  "clients-book-deposits",
  function () {
    return JSON.parse(open(`../data/clients.${environment}.deposits.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);
globalThis.depositsListSubUsers = new SharedArray(
  "clients-deposits-List",
  function () {
    return JSON.parse(
      open(`../data/clients.${environment}.depositList.json`),
    ).map((user: any) => ({ ...user, token: "" }));
  },
);
globalThis.cibAccountNumbers = new SharedArray(
  "cib-account-numbers",
  function () {
    return JSON.parse(open(`../data/cibAccountNumbers.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.creditCardSubUsers = new SharedArray(
  "clients-credit-cards",
  function () {
    return JSON.parse(open(`../data/clients.per.creditCards.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.debitCardSubUsers = new SharedArray(
  "clients-debit-cards",
  function () {
    return JSON.parse(open(`../data/clients.per.debitCards.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.creditCardsInstallmentSubUser = new SharedArray(
  "clients-credit-cards-Installment",
  function () {
    return JSON.parse(
      open(`../data/clients.per.creditCardsInstallment.json`),
    ).map((user: any) => ({ ...user, token: "" }));
  },
);
//npm run build
globalThis.ccTransferSubUsers = new SharedArray(
  "clients-cc-transfer",
  function () {
    return JSON.parse(open(`../data/clients.per.cctransfer.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.offlineRequestSubUsers = new SharedArray(
  "clients-offline-requests",
  function () {
    return JSON.parse(open(`../data/clients.per.offline-request.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);
globalThis.custodySubUsers = new SharedArray(
  "clients-stocks",
  function () {
    return JSON.parse(open(`../data/clients.${environment}.stocks.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.ipnTransferSubUsers = new SharedArray(
  "clients-ipn-transfer",
  function () {
    return JSON.parse(open(`../data/clients.${environment}.ipn.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.annexSubUsers = new SharedArray(
  "clients-annex",
  function () {
    return JSON.parse(open(`../data/clients.${environment}.annex.json`)).map(
      (user: any) => ({ ...user, token: "" }),
    );
  },
);

globalThis.excludedUsers = [];
globalThis.excludedBiometricUsers = [];
globalThis.runLoads = virtualUsers;
globalThis.scenarioLoads = loadArray;

globalThis.resetPassword = "NewPassword@202";
globalThis.passwordWasReset = false;

export function setupVu() {
  globalThis.api = new Httpx({ baseUrl: apiBaseUrl });
  globalThis.idp = new Httpx({ baseUrl: idpBaseUrl });
}
