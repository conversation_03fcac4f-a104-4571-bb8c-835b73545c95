import { batch } from "k6/http";
import { setupVu } from "./core/setup";
import {
  accessHomePageExec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollment,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageCr2Exec,
  accessHomePageMs,
  createTransferInsideCIB,
  accountTransaction,
  createOutSideCibTransfer,
  creditCardTransaction,
  accountTransactionDetails,
  accountTransactionDetailsExec,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userAccountMovementDetails: accountTransactionDetails("load"),
  },
};

export {
  accessHomePageExec,
  accessHomePageCr2Exec,
  accountTransactionExec,
  creditCardTransactionExec,
  estatementEnrollmentExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accountTransactionDetailsExec,
};
