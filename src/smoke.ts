import { setupVu } from "./core/setup";
import {
  createTransferInsideCIB,
  createTransferInsideCIBExec,
  accessHomePage,
  accessHomePageExec,
  estatementEnrollment,
  estatementEnrollmentExec,
  creditCardTransaction,
  creditCardTransactionExec,
  accessHomePageCr2Exec,
  accessHomePageMs,
  accessHomePageCr2,
  accountTransaction,
  accountTransactionExec,
  bookCertificateDeposit,
  bookCertificateDepositExec,
  createOutSideCibTransfer,
  createTransferOutsideCIBExec,
  creditCardTransfer,
  creditCardTransferExec,
  billPayment,
  billPaymentExec,
  depositDetails,
  depositDetailsExec,
  creditCardInstallementExec,
  creditCardInstallment,
  accountTransactionDetails,
  accountTransactionDetailsExec,
  debitCardListExec,
  debitCardList, ipnTransfer, BiometricLogin,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userLoggingIn: accessHomePageMs("smoke"),
    creditCardTransaction: creditCardTransfer("smoke"),
    userAccountMovement: accountTransaction("smoke"),
    userAccountMovementDetails: accountTransactionDetails("smoke"),
    userCreditCardInstallment: creditCardInstallment("smoke"),
    userCreditCardMovement: creditCardTransaction("smoke"),
    userSubscribingToEstatement: estatementEnrollment("smoke"),
    userTransferInsideCib: createTransferInsideCIB("smoke"),
    userTransferOutsideCib: createOutSideCibTransfer("smoke"),
    debitCardList: debitCardList("smoke"),
    userIpnTransfer: ipnTransfer("smoke"),
    biometricLogin: BiometricLogin('smoke')

  },
  thresholds: {
    http_req_duration: ["p(95)<3000"],
    http_req_failed: ["rate<0.001"],
  },
};

export {
  creditCardTransactionExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageExec,
  accessHomePageCr2Exec,
  estatementEnrollmentExec,
  accountTransactionExec,
  bookCertificateDepositExec,
  creditCardTransferExec,
  billPaymentExec,
  depositDetailsExec,
  creditCardInstallementExec,
  accountTransactionDetailsExec,
  debitCardListExec,
};
