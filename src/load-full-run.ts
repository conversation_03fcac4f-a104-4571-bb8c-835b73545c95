import { setupVu } from "./core/setup";
import {
  createTransferInsideCIB,
  createTransferInsideCIBExec,
  accessHomePageExec,
  estatementEnrollment,
  estatementEnrollmentExec,
  creditCardTransaction,
  creditCardTransactionExec,
  accessHomePageCr2Exec,
  accessHomePageMs,
  accountTransaction,
  accountTransactionExec,
  bookCertificateDepositExec,
  createOutSideCibTransfer,
  createTransferOutsideCIBExec,
  creditCardTransfer,
  creditCardTransferExec,
  billPaymentExec,
  depositDetailsExec,
  creditCardInstallementExec,
  creditCardInstallment,
  accountTransactionDetails,
  accountTransactionDetailsExec,
  debitCardListExec,
  debitCardList, ipnTransfer, BiometricLogin,
} from "./scenarios";

setupVu();

export const options = {
  batch: 10,
  batchPerHost: 5,
  noVUConnectionReuse: true,
  scenarios: {
    userLoggingIn: accessHomePageMs("load"),
    // creditCardTransaction: creditCardTransfer("load"),
    userAccountMovement: accountTransaction("load"),
    userAccountMovementDetails: accountTransactionDetails("load"),
    // userCreditCardInstallment: creditCardInstallment("load"),
    // userCreditCardMovement: creditCardTransaction("load"),
    userSubscribingToEstatement: estatementEnrollment("load"),
    userTransferInsideCib: createTransferInsideCIB("load"),
    userTransferOutsideCib: createOutSideCibTransfer("load"),
    // debitCardList: debitCardList("load"),
    userIpnTransfer: ipnTransfer("load"),
    biometricLogin : BiometricLogin('load')

  },
  thresholds: {
    //failure rate should be less than 10%
    http_req_failed: ["rate<0.1"],
    //http_req_duration should be less than 5000ms
    http_req_duration: ["p(95)<5000"],
  },
};

export {
  creditCardTransactionExec,
  createTransferInsideCIBExec,
  createTransferOutsideCIBExec,
  accessHomePageExec,
  accessHomePageCr2Exec,
  estatementEnrollmentExec,
  accountTransactionExec,
  bookCertificateDepositExec,
  creditCardTransferExec,
  billPaymentExec,
  depositDetailsExec,
  creditCardInstallementExec,
  accountTransactionDetailsExec,
  debitCardListExec,
};
