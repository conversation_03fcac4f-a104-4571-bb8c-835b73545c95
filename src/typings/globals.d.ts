import { Httpx } from "../core/httpx";

type User = {
  username: string;
  password: string;
  accountNumber: string;
  token: string;
};
export type BiometricUsers =  {
  "user": string
  "data": {
    "deviceId": string
    "payload": {
      cipherKey: string
      kcUserId: string
      deviceId: string
    }
    signature : string
    cipherKey: string
    kcUserId: string
    "nonce": string
  }
}

type Account = {
  accountNumber: string;
  name: string;
};

type ScenarioLoad = {
  type: "load" | "stress" | "endurance" | "smoke" | "shock";
  scenario: string;
  load: number;
};

type RunLoad = {
  type: "load" | "stress" | "endurance" | "smoke" | "shock";
  load: number;
};

declare global {
  var idp: Httpx;
  var api: Httpx;
  var users: User[];
  var excludedUsers: User[];
  var excludedBiometricUsers : BiometricUsers[];
  var accountsSubUsers: User[];
  var accountsTransactionsSubUsers: User[];
  var transfersInsideCibSubUsers: User[];
  var billPaymentSubUsers: User[];
  var transfersOutsideCibSubUsers: User[];
   var custodySubUsers: User[];

  var bookDepositsSubUsers: User[];

  var cibAccountNumbers: Account[];

  var creditCardSubUsers: User[];
  var debitCardSubUsers: User[];

  var ccTransferSubUsers: User[];
  var depositsListSubUsers: User[];
  var creditCardsInstallmentSubUser: User[];
  var offlineRequestSubUsers: User[];
  var ipnTransferSubUsers: User[];
    var annexSubUsers: User[];
  var BiometricLoginUsers: BiometricUsers[];



  var scenarioLoads: ScenarioLoad[];
  var runLoads: RunLoad[];
  var resetPassword: string;
  var passwordWasReset: boolean;
}
