image: registry.apps.ocp-nonprod-01.hodomain.local/dso/docker-images/ci-all-in-one:node-v18.15.0_java-v17.0.6-20240402104233

stages:
  - prepare
  - package
  - warmup
  - run

variables:
  HOME: $CI_BUILDS_DIR
  NPM_REGISTRY: https://nexus.apps.ocp-nonprod-01.hodomain.local/repository/npm-group/
  NO_PROXY: svc.cluster.local,hodomain.local
  IDP_BASE_URL: https://idp-per.apps.ocp-nonprod-02.hodomain.local
  API_BASE_URL: https://api-per.apps.ocp-nonprod-02.hodomain.local
  ENVIRONMENT: per
  IMAGE: registry.apps.ocp-nonprod-01.hodomain.local/library/k6-executor:v0.0.1-20231031182635
  K6_PROMETHEUS_RW_SERVER_URL: http://prometheus.apps.ocp-nonprod-01.hodomain.local/api/v1/write

install_dependencies:
  stage: prepare
  variables:
    KUBERNETES_MEMORY_REQUEST: 8Gi
    KUBERNETES_MEMORY_LIMIT: 8Gi
  before_script:
    - yarn config set registry $NPM_REGISTRY
    - yarn config set cache-folder .cache/yarn
  script:
    - https_proxy=http://cibproxy.hodomain.local:8080 yarn install --frozen-lockfile --prefer-offline
  cache:
    key:
      files:
        - yarn.lock
    paths:
      - node_modules
    policy: pull-push

package_load_scripts:
  stage: package
  script:
    - yarn build
    - ls -la dist
  cache:
    key:
      files:
        - yarn.lock
    paths:
      - node_modules
    policy: pull
  artifacts:
    paths:
      - dist/*.js

shock_run:
  stage: warmup
  image: $IMAGE
  variables:
    K6_PROMETHEUS_RW_TREND_AS_NATIVE_HISTOGRAM: "true"
    K6_PROMETHEUS_RW_PUSH_INTERVAL: 2s
  script:
    - k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_shock --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/shock.js
  timeout: 30m
  tags:
    - k8s-02
  allow_failure: true

run_full_test:
  stage: run
  image: $IMAGE
  variables:
    K6_PROMETHEUS_RW_TREND_AS_NATIVE_HISTOGRAM: "true"
    K6_PROMETHEUS_RW_PUSH_INTERVAL: 2s
  script:
    - k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_full_test --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-full-run.js
  timeout: 1h
  tags:
    - k8s-02
  when: manual
  allow_failure: true

run_ms_load:
  stage: run
  image: $IMAGE
  variables:
    K6_PROMETHEUS_RW_TREND_AS_NATIVE_HISTOGRAM: "true"
    K6_PROMETHEUS_RW_PUSH_INTERVAL: 2s
    KUBERNETES_CPU_REQUEST: "4"
    KUBERNETES_CPU_LIMIT: "4"
    KUBERNETES_MEMORY_REQUEST: 8Gi
    KUBERNETES_MEMORY_LIMIT: 14Gi
  script:

      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_ipn_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-ipn.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_login --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-generic.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_offline-request --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-offline-request.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_acc_movement --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-account-movement.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_acc_movement-details --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-account-movement-details.js &

      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_debit_list --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-debit-list.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_deposits --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-deposits.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_deposits-details --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-deposits-list.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_bill-payment --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-bill-payment.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_cc_movement --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-cc-movement.js &
            k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_out_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-outside-transfer.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_in_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-inside-transfer.js &
      k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_cc_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-creditcard-transfer.js
    # k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_biomertic_login --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-biometric-login.js &
      # k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_creditcardinstallment --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-cc-installment.js &


    # k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_annex --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-annex-download.js


  timeout: 15h
  tags:
    - k8s-02
  when: manual

# run_ms_endurance:
#   stage: run
#   image: $IMAGE
#   variables:
#     K6_PROMETHEUS_RW_TREND_AS_NATIVE_HISTOGRAM: "true"
#     K6_PROMETHEUS_RW_PUSH_INTERVAL: 2s
#   script:
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_login --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-generic.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_creditcardinstallment --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-cc-installment.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_out_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-outside-transfer.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_deposits --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-deposits.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_deposits-details --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-deposits-list.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_in_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-inside-transfer.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_cc_movement --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-cc-movement.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_acc_movement --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-account-movement.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_acc_movement-details --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-account-movement-details.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_cc_transfer --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-creditcard-transfer.js &
#     k6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_bill-payment --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-bill-payment.js &
#     K6 run --insecure-skip-tls-verify --tag testid=${CI_JOB_ID}_offline-request --tag type=ms --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-offline-request.js
#   timeout: 15h
#   tags:
#     - k8s-02
#   when: manual

run_cr2_load:
  stage: run
  image: $IMAGE
  variables:
    K6_PROMETHEUS_RW_TREND_AS_NATIVE_HISTOGRAM: "true"
    K6_PROMETHEUS_RW_PUSH_INTERVAL: 2s
  script:
    - k6 run --insecure-skip-tls-verify --tag testid=$CI_JOB_ID --tag type=cr2 --tag environment=$ENVIRONMENT -o xk6-prometheus-rw dist/load-cr2.js
  timeout: 3h
  tags:
    - k8s-02
  when: manual
